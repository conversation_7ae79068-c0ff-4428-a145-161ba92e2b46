const axios = require('axios');
require('dotenv').config();




const BASE_URL = `http://localhost:${process.env.PORT || 3001}`;


async function testTool(tool, params = {}) {
  console.log(`\n===== Testing tool: ${tool} =====`);
  console.log('Params:', JSON.stringify(params, null, 2));

  try {



    const response = await axios.post(`${BASE_URL}/tools/${tool}`, {
      params
    });
    


    console.log('Status:', response.status);
    console.log('Response:', JSON.stringify(response.data, null, 2));
    
    return response.data;
  } catch (error) {




    console.error('Error:', error.response?.data || error.message);
    throw error;
  }
}

async function runTests() {

  try {
    console.log('Starting XOps MCP Server Tool Tests');
    console.log('==================================');
    console.log(`API Endpoint: ${process.env.FIREBASE_ENDPOINT}`);
    console.log(`Using API Key: ${process.env.API_KEY ? '✅ Set' : '❌ Missing'}`);

    // Test server info
    const infoResponse = await axios.get(BASE_URL);
    console.log('\n===== Server Info =====');
    console.log(JSON.stringify(infoResponse.data, null, 2));

    // Test health endpoint
    const healthResponse = await axios.get(`${BASE_URL}/health`);





    console.log('\n===== Health Check =====');
    console.log(JSON.stringify(healthResponse.data, null, 2));

    // Test tools endpoint
    const toolsResponse = await axios.get(`${BASE_URL}/tools`);
    console.log('\n===== Available Tools =====');
    console.log(JSON.stringify(toolsResponse.data, null, 2));
  // Test change-persona tool




    await testTool('change-persona', { personaName: 'developer' });
  // Test fetch-template tool




    await testTool('fetch-template', { templateName: 'api-design' });
  // Test run-checklist tool
  await testTool('run-checklist', {


      checklistName: 'code-review',
      documentContent: `
        # Sample Code

        This is a sample document for testing the checklist functionality.
        It includes some code and documentation elements.

        \`\`\`javascript
        function testFunction() {
          // This function has comments
          console.log('Test');
          return true;
        }
        \`\`\`

        ## Error Handling

        This code includes error handling and has been tested.
      `
  });
  
  // Test load-task tool




    await testTool('load-task', { taskName: 'setup-firebase' });
  // Test save-context tool
  await testTool('save-context', {
    contextName: 'test-context',
    contextData: {
        project: 'XOps Firebase Integration',
        currentTask: 'Testing',
        notes: 'This is a test context'
    }
  });

  // Test load-context tool
  await testTool('load-context', {
    contextName: 'test-context'
  });

  // Test get-config tool
  try {
    const configResponse = await axios.get(`${BASE_URL}/tools/get-config`);
    console.log('\n===== Configuration =====');
    console.log('Status:', configResponse.status);
    console.log('Response:', JSON.stringify(configResponse.data, null, 2));
  } catch (error) {
    console.error('❌ get-config test failed:', error.response?.data || error.message);
  }

  // Test get state
  const stateResponse = await axios.get(`${BASE_URL}/tools/state`);
  console.log('\n===== Current State =====');
  console.log(JSON.stringify(stateResponse.data, null, 2));

  // Test clear state
  await testTool('clear-state');

  console.log('\n===== All Tests Completed Successfully =====');
  } catch (error) {

    console.error('\n❌ Test Failed:', error.message);
    process.exit(1);
  }
}

runTests();