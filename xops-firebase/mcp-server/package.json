{"name": "xops-mcp-server", "version": "0.1.0", "description": "MCP Server for XOps Firebase Integration with continue.dev", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test-tool": "node test-tool.js", "test-integration": "node test-integration.js"}, "keywords": ["xops", "firebase", "continue.dev", "mcp", "server"], "author": "", "license": "ISC", "dependencies": {"axios": "^1.4.0", "body-parser": "^1.20.2", "colors": "^1.4.0", "cors": "^2.8.5", "dotenv": "^16.0.3", "express": "^4.18.2", "helmet": "^6.1.5"}, "devDependencies": {"jest": "^29.5.0", "nodemon": "^3.1.10", "supertest": "^6.3.3"}}