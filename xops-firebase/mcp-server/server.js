require('dotenv').config();
const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const bodyParser = require('body-parser');

// Initialize Express app
const app = express();

// Apply middleware
app.use(cors());
app.use(helmet());
app.use(bodyParser.json());

// Import routes and middleware
const toolRoutes = require('./src/routes/toolRoutes');
const { errorHandler } = require('./src/middleware/errorHandler');

// Root route for informational landing page
app.get('/', (req, res) => {
  res.json({
    name: 'XOps MCP Server',
    version: '0.1.0',
    description: 'MCP Server for XOps Firebase Integration with continue.dev',
    endpoints: {
      health: '/health',
      tools: '/tools'
    },
    documentation: 'See README.md for more information',
    timestamp: new Date().toISOString()
  });
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({ 
    status: 'ok', 
    timestamp: new Date().toISOString(),
    server: 'XOps MCP Server'
  });
});

// Use routes
app.use('/tools', toolRoutes);

// Enhanced error handling middleware
app.use(errorHandler);

// 404 handler for undefined routes
app.use((req, res) => {
  res.status(404).json({
    error: 'Not Found',
    message: `Cannot ${req.method} ${req.path}`,
    availableEndpoints: {
      '/': 'GET - Server information',
      '/health': 'GET - Health check',
      '/tools': 'GET - Available tools information',
      '/tools/change-persona': 'POST - Change active persona',
      '/tools/fetch-template': 'POST - Fetch a template',
      '/tools/run-checklist': 'POST - Run a checklist against a document',
      '/tools/load-task': 'POST - Load a task',
      '/tools/save-context': 'POST - Save context data',
      '/tools/load-context': 'POST - Load context data',
      '/tools/get-config': 'GET - Get configuration',
      '/tools/state': 'GET - Get current state',
      '/tools/clear-state': 'POST - Clear current state'
    }
  });
});

// Set up port
const PORT = process.env.PORT || 3001;

// Start server
app.listen(PORT, () => {
  console.log(`MCP Server running on port ${PORT}`);
  console.log(`Server info: http://localhost:${PORT}/`);
  console.log(`Health check: http://localhost:${PORT}/health`);
  console.log(`Tools endpoint: http://localhost:${PORT}/tools`);
});