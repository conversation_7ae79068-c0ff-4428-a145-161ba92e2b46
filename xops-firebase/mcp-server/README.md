# XOps MCP Server

A Model Context Protocol (MCP) server for XOps Firebase Integration with continue.dev. This server provides AI orchestration tools for persona management, task workflows, templates, checklists, and context management.

## Features

- **Config-Driven Authority**: All personas and tasks are defined in a centralized configuration
- **State Management**: Persistent state tracking across sessions
- **Context Management**: Save and restore work contexts
- **Enhanced Error Handling**: Comprehensive error handling with user-friendly messages
- **Status Bar Integration**: Real-time status updates in continue.dev
- **Comprehensive Testing**: Unit and integration tests included

## Available Tools

### Core Tools

1. **change-persona**: Switch between AI personas based on configuration
2. **fetch-template**: Retrieve templates for specific tasks
3. **run-checklist**: Execute checklists against documents
4. **load-task**: Load task workflows for the active persona
5. **save-context**: Save current work context for later restoration
6. **load-context**: Load previously saved work contexts

### Configuration & State Tools

1. **get-config**: Retrieve the current XOps configuration
2. **state**: Get current server state (debugging)
3. **clear-state**: Clear current server state (debugging)

## Setup

Install dependencies:

```bash
npm install
```

Configure .env:

```env
PORT=3001
FIREBASE_ENDPOINT=https://us-central1-your-project-id.cloudfunctions.net/xopsAgent
API_KEY=your-api-key
```

## Usage

Start server:

```bash
npm start
```

Development mode:

```bash
npm run dev
```

Server available at: <http://localhost:3001>

## Testing

Run unit tests:

```bash
npm test
```

Run integration tests:

```bash
npm run test-integration
```

Test individual tools:

```bash
npm run test-tool
```

## API Documentation

### Base URL

All endpoints are available at: `http://localhost:3001`

### Authentication

Include your API key in the `X-API-Key` header for Firebase API calls.

### Tool Endpoints

#### 1. Change Persona

Switch the active AI persona based on configuration.

**Endpoint:** `POST /tools/change-persona`

**Request Body:**

```json
{
  "params": {
    "personaName": "bmad-advisor"
  }
}
```

**Response:**

```json
{
  "result": {
    "success": true,
    "persona": "bmad-advisor",
    "title": "BMad Method Advisor",
    "description": "Business Model Advisory expert for startup strategy",
    "content": "# BMad Method Advisor\n...",
    "availableTasks": [...]
  },
  "statusBar": {
    "text": "Active Persona: BMad Method Advisor",
    "icon": "🧠"
  }
}
```

#### 2. Load Task

Load a task workflow for the active persona.

**Endpoint:** `POST /tools/load-task`

**Request Body:**

```json
{
  "params": {
    "taskName": "Business Model Canvas",
    "personaName": "bmad-advisor"
  }
}
```

**Response:**

```json
{
  "result": {
    "success": true,
    "task": "Business Model Canvas",
    "taskInfo": {
      "name": "Business Model Canvas",
      "description": "Create a Business Model Canvas",
      "taskFile": "bmad/business-model-canvas.md"
    },
    "content": "# Business Model Canvas\n...",
    "persona": "bmad-advisor"
  },
  "statusBar": {
    "text": "Active Task: Create a Business Model Canvas",
    "icon": "📋"
  }
}
```

#### 3. Save Context

Save the current work context for later restoration.

**Endpoint:** `POST /tools/save-context`

**Request Body:**

```json
{
  "params": {
    "contextName": "my-project-context",
    "contextData": {
      "project": "My Startup",
      "currentTask": "Business Model Canvas",
      "notes": "Working on customer segments",
      "progress": 50
    },
    "useFirebase": false
  }
}
```

**Response:**

```json
{
  "result": {
    "success": true,
    "contextName": "my-project-context",
    "storedIn": "local only"
  },
  "statusBar": {
    "text": "Context saved: my-project-context",
    "icon": "💾"
  }
}
```

#### 4. Load Context

Load a previously saved work context.

**Endpoint:** `POST /tools/load-context`

**Request Body:**

```json
{
  "params": {
    "contextName": "my-project-context",
    "useFirebase": false
  }
}
```

**Response:**

```json
{
  "result": {
    "success": true,
    "contextName": "my-project-context",
    "contextData": {
      "project": "My Startup",
      "currentTask": "Business Model Canvas",
      "notes": "Working on customer segments",
      "progress": 50,
      "_metadata": {
        "name": "my-project-context",
        "createdAt": "2024-01-15T10:30:00.000Z",
        "creator": "mcp-server"
      }
    },
    "source": "local"
  },
  "statusBar": {
    "text": "Context loaded: my-project-context",
    "icon": "📂"
  }
}
```

#### 5. Get Configuration

Retrieve the current XOps configuration.

**Endpoint:** `GET /tools/get-config`

**Response:**

```json
{
  "result": {
    "success": true,
    "config": {
      "title": "XOps Orchestrator",
      "version": "1.0.0",
      "dataResolution": {
        "personas": "personas",
        "tasks": "tasks",
        "templates": "templates",
        "checklists": "checklists"
      },
      "personas": [...]
    },
    "metadata": {
      "loaded": true,
      "title": "XOps Orchestrator",
      "version": "1.0.0",
      "personaCount": 2,
      "lastLoadTime": "2024-01-15T10:30:00.000Z",
      "cacheValid": true
    }
  },
  "statusBar": {
    "text": "Config loaded: XOps Orchestrator v1.0.0",
    "icon": "⚙️"
  }
}
```

### Error Handling

All endpoints return consistent error responses:

```json
{
  "error": "Error message",
  "type": "ERROR_TYPE",
  "statusCode": 400,
  "details": "Additional error details",
  "statusBar": {
    "text": "Error: Error message",
    "icon": "⚠️"
  },
  "requestId": "abc123",
  "timestamp": "2024-01-15T10:30:00.000Z",
  "path": "/tools/change-persona",
  "method": "POST"
}
```

### Error Types

- `VALIDATION_ERROR`: Invalid request parameters
- `NOT_FOUND_ERROR`: Resource not found
- `CONFIG_ERROR`: Configuration loading/parsing error
- `CONTEXT_ERROR`: Context management error
- `EXTERNAL_API_ERROR`: Firebase API error
- `INTERNAL_ERROR`: Server internal error

## Configuration

The server uses a config-driven architecture. All personas, tasks, and resources are defined in a central configuration file stored in Firebase.

### Configuration Structure

```json
{
  "title": "XOps Orchestrator",
  "version": "1.0.0",
  "dataResolution": {
    "personas": "personas",
    "tasks": "tasks",
    "templates": "templates",
    "checklists": "checklists",
    "data": "data"
  },
  "personas": [
    {
      "name": "bmad-advisor",
      "title": "BMad Method Advisor",
      "description": "Business Model Advisory expert for startup strategy",
      "persona": "bmad-advisor.md",
      "tasks": [
        {
          "name": "Business Model Canvas",
          "description": "Create a Business Model Canvas",
          "taskFile": "bmad/business-model-canvas.md"
        }
      ]
    }
  ]
}
```

## Architecture

### Components

- **Config Service**: Loads and manages configuration from Firebase
- **State Manager**: Tracks active persona, tasks, and session context
- **Context Service**: Manages context saving/loading with Firebase fallback
- **API Client**: Handles Firebase API communication with retry logic
- **Error Handler**: Provides comprehensive error handling and logging

### Design Principles

1. **Config-Driven Authority**: All personas and tasks defined in configuration
2. **State Persistence**: Maintain state across sessions
3. **Graceful Degradation**: Local fallbacks when Firebase is unavailable
4. **Comprehensive Error Handling**: User-friendly error messages
5. **Status Bar Integration**: Real-time feedback in continue.dev

## Deployment

### Local Development

1. Clone the repository
2. Install dependencies: `npm install`
3. Configure environment variables
4. Start development server: `npm run dev`

### Production Deployment

The server can be deployed to various platforms:

#### Docker

```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
EXPOSE 3001
CMD ["npm", "start"]
```

#### Cloud Run

```bash
gcloud run deploy xops-mcp-server \
  --source . \
  --platform managed \
  --region us-central1 \
  --allow-unauthenticated
```

## Troubleshooting

### Common Issues

1. **Firebase Connection Errors**
   - Check `FIREBASE_ENDPOINT` and `API_KEY` in `.env`
   - Verify Firebase Function is deployed and accessible

2. **Configuration Loading Errors**
   - Ensure `config/config.json` exists in Firebase Storage
   - Validate JSON structure against schema

3. **Persona Not Found**
   - Check persona name matches configuration
   - Reload configuration with `get-config` endpoint

4. **Context Loading Errors**
   - Verify context was saved successfully
   - Check local storage and Firebase availability

### Debug Mode

Enable debug logging by setting:

```env
NODE_ENV=development
```

### Health Check

Monitor server health at: `GET /health`

## Contributing

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Ensure all tests pass
5. Submit a pull request

## License

This project is licensed under the ISC License.