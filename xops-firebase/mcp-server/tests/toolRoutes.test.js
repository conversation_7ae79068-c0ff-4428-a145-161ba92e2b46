/**
 * Integration tests for tool routes
 */

const request = require('supertest');
const express = require('express');
const toolRoutes = require('../src/routes/toolRoutes');

// Mock dependencies
jest.mock('../src/utils/apiClient');
jest.mock('../src/utils/stateManager');
jest.mock('../src/utils/contextService');
jest.mock('../src/utils/configService');

const ApiClient = require('../src/utils/apiClient');
const stateManager = require('../src/utils/stateManager');
const contextService = require('../src/utils/contextService');
const configService = require('../src/utils/configService');

describe('Tool Routes', () => {
  let app;
  
  const mockConfig = {
    title: 'Test XOps',
    version: '1.0.0',
    dataResolution: {
      personas: 'personas',
      tasks: 'tasks',
      templates: 'templates',
      checklists: 'checklists'
    },
    personas: [
      {
        name: 'test-advisor',
        title: 'Test Advisor',
        description: 'Test persona',
        persona: 'test-advisor.md',
        tasks: [
          {
            name: 'Test Task',
            description: 'A test task',
            taskFile: 'test/test-task.md'
          }
        ]
      }
    ]
  };
  
  beforeEach(() => {
    // Create Express app with routes
    app = express();
    app.use(express.json());
    app.use('/tools', toolRoutes);
    
    // Clear all mocks
    jest.clearAllMocks();
    
    // Setup default mocks
    ApiClient.prototype.getFile = jest.fn();
    stateManager.setActivePersona = jest.fn();
    stateManager.getActivePersona = jest.fn();
    stateManager.setSessionContext = jest.fn();
    stateManager.getSessionContext = jest.fn();
    stateManager.getState = jest.fn().mockReturnValue({});
    stateManager.clearState = jest.fn().mockReturnValue({});
    
    contextService.saveContext = jest.fn();
    contextService.loadContext = jest.fn();
    
    configService.getConfig = jest.fn();
    configService.loadConfig = jest.fn();
    configService.findPersona = jest.fn();
    configService.getDataResolution = jest.fn();
    configService.getConfigMetadata = jest.fn();
  });
  
  describe('GET /tools', () => {
    it('should return available tools information', async () => {
      const response = await request(app).get('/tools');
      
      expect(response.status).toBe(200);
      expect(response.body.message).toBe('XOps MCP Server Tools API');
      expect(response.body.availableTools).toBeDefined();
      expect(response.body.debugTools).toBeDefined();
    });
  });
  
  describe('POST /tools/change-persona', () => {
    it('should change persona successfully', async () => {
      // Arrange
      configService.getConfig.mockReturnValue(mockConfig);
      configService.findPersona.mockReturnValue(mockConfig.personas[0]);
      configService.getDataResolution.mockReturnValue(mockConfig.dataResolution);
      ApiClient.prototype.getFile.mockResolvedValue('# Test Persona Content');
      
      // Act
      const response = await request(app)
        .post('/tools/change-persona')
        .send({ params: { personaName: 'test-advisor' } });
      
      // Assert
      expect(response.status).toBe(200);
      expect(response.body.result.success).toBe(true);
      expect(response.body.result.persona).toBe('test-advisor');
      expect(response.body.statusBar.text).toContain('Test Advisor');
      expect(stateManager.setActivePersona).toHaveBeenCalledWith('test-advisor');
    });
    
    it('should return error for missing persona name', async () => {
      const response = await request(app)
        .post('/tools/change-persona')
        .send({ params: {} });
      
      expect(response.status).toBe(400);
      expect(response.body.error).toContain('Missing required parameter: personaName');
    });
    
    it('should return error for non-existent persona', async () => {
      // Arrange
      configService.getConfig.mockReturnValue(mockConfig);
      configService.findPersona.mockReturnValue(null);
      configService.getPersonas.mockReturnValue(mockConfig.personas);
      
      // Act
      const response = await request(app)
        .post('/tools/change-persona')
        .send({ params: { personaName: 'non-existent' } });
      
      // Assert
      expect(response.status).toBe(400);
      expect(response.body.error).toContain('not found in configuration');
      expect(response.body.availablePersonas).toBeDefined();
    });
  });
  
  describe('POST /tools/load-context', () => {
    it('should load context successfully', async () => {
      // Arrange
      const mockContextData = { project: 'Test', task: 'Testing' };
      contextService.loadContext.mockResolvedValue({
        success: true,
        contextName: 'test-context',
        contextData: mockContextData,
        source: 'local'
      });
      
      // Act
      const response = await request(app)
        .post('/tools/load-context')
        .send({ params: { contextName: 'test-context' } });
      
      // Assert
      expect(response.status).toBe(200);
      expect(response.body.result.success).toBe(true);
      expect(response.body.result.contextName).toBe('test-context');
      expect(response.body.result.contextData).toEqual(mockContextData);
      expect(response.body.statusBar.text).toContain('Context loaded');
    });
    
    it('should return error for missing context name', async () => {
      const response = await request(app)
        .post('/tools/load-context')
        .send({ params: {} });
      
      expect(response.status).toBe(400);
      expect(response.body.error).toContain('Missing required parameter: contextName');
    });
    
    it('should handle context not found error', async () => {
      // Arrange
      contextService.loadContext.mockRejectedValue(new Error('Context not found'));
      
      // Act
      const response = await request(app)
        .post('/tools/load-context')
        .send({ params: { contextName: 'non-existent' } });
      
      // Assert
      expect(response.status).toBe(500);
      expect(response.body.error).toContain('Context not found');
    });
  });
  
  describe('GET /tools/get-config', () => {
    it('should return config from cache', async () => {
      // Arrange
      configService.getConfig.mockReturnValue(mockConfig);
      configService.getConfigMetadata.mockReturnValue({
        loaded: true,
        title: mockConfig.title,
        version: mockConfig.version,
        personaCount: mockConfig.personas.length
      });
      
      // Act
      const response = await request(app).get('/tools/get-config');
      
      // Assert
      expect(response.status).toBe(200);
      expect(response.body.result.success).toBe(true);
      expect(response.body.result.config).toEqual(mockConfig);
      expect(response.body.statusBar.text).toContain('Test XOps v1.0.0');
    });
    
    it('should load config from Firebase if not cached', async () => {
      // Arrange
      configService.getConfig.mockReturnValue(null);
      configService.loadConfig.mockResolvedValue(mockConfig);
      configService.getConfigMetadata.mockReturnValue({
        loaded: true,
        title: mockConfig.title,
        version: mockConfig.version,
        personaCount: mockConfig.personas.length
      });
      
      // Act
      const response = await request(app).get('/tools/get-config');
      
      // Assert
      expect(response.status).toBe(200);
      expect(response.body.result.success).toBe(true);
      expect(configService.loadConfig).toHaveBeenCalled();
    });
    
    it('should handle config loading error', async () => {
      // Arrange
      configService.getConfig.mockReturnValue(null);
      configService.loadConfig.mockRejectedValue(new Error('Config load failed'));
      
      // Act
      const response = await request(app).get('/tools/get-config');
      
      // Assert
      expect(response.status).toBe(500);
      expect(response.body.error).toContain('Config load failed');
    });
  });
  
  describe('POST /tools/save-context', () => {
    it('should save context successfully', async () => {
      // Arrange
      const mockContextData = { project: 'Test', task: 'Testing' };
      contextService.saveContext.mockResolvedValue({
        success: true,
        contextName: 'test-context',
        storedIn: 'local only'
      });
      
      // Act
      const response = await request(app)
        .post('/tools/save-context')
        .send({ 
          params: { 
            contextName: 'test-context', 
            contextData: mockContextData 
          } 
        });
      
      // Assert
      expect(response.status).toBe(200);
      expect(response.body.result.success).toBe(true);
      expect(response.body.result.contextName).toBe('test-context');
      expect(response.body.statusBar.text).toContain('Context saved');
      expect(contextService.saveContext).toHaveBeenCalledWith(
        'test-context',
        mockContextData,
        expect.objectContaining({ creator: 'mcp-server' })
      );
    });
    
    it('should return error for missing context data', async () => {
      const response = await request(app)
        .post('/tools/save-context')
        .send({ params: { contextName: 'test' } });
      
      expect(response.status).toBe(400);
      expect(response.body.error).toContain('Missing required parameter: contextData');
    });
  });
  
  describe('GET /tools/state', () => {
    it('should return current state', async () => {
      // Arrange
      const mockState = { activePersona: 'test', sessionContext: {} };
      stateManager.getState.mockReturnValue(mockState);
      
      // Act
      const response = await request(app).get('/tools/state');
      
      // Assert
      expect(response.status).toBe(200);
      expect(response.body.result).toEqual(mockState);
      expect(response.body.statusBar.text).toContain('State retrieved');
    });
  });
  
  describe('POST /tools/clear-state', () => {
    it('should clear state successfully', async () => {
      // Arrange
      const mockClearResult = { success: true, state: {} };
      stateManager.clearState.mockReturnValue(mockClearResult);
      
      // Act
      const response = await request(app).post('/tools/clear-state');
      
      // Assert
      expect(response.status).toBe(200);
      expect(response.body.result).toEqual(mockClearResult);
      expect(response.body.statusBar.text).toContain('State cleared');
    });
  });
});
