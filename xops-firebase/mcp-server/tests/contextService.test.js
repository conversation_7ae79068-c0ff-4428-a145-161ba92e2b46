/**
 * Unit tests for the Context Service
 */

// Mock dependencies first
jest.mock('../src/utils/apiClient');
jest.mock('../src/utils/stateManager');

const contextService = require('../src/utils/contextService');
const ApiClient = require('../src/utils/apiClient');
const stateManager = require('../src/utils/stateManager');

describe('ContextService', () => {
  let mockApiClient;
  
  const mockContextData = {
    project: 'Test Project',
    currentTask: 'Testing',
    notes: 'Test notes'
  };
  
  beforeEach(() => {
    // Clear all mocks
    jest.clearAllMocks();
    
    // Mock ApiClient
    mockApiClient = {
      getFile: jest.fn()
    };
    ApiClient.mockImplementation(() => mockApiClient);
    
    // Mock stateManager
    stateManager.setSessionContext = jest.fn();
    stateManager.getSessionContext = jest.fn();
    stateManager.getState = jest.fn().mockReturnValue({ sessionContext: {} });

    // Reset the singleton instance
    contextService.localContexts = {};
  });
  
  describe('saveContext', () => {
    it('should save context successfully', async () => {
      // Act
      const result = await contextService.saveContext('test-context', mockContextData);
      
      // Assert
      expect(result.success).toBe(true);
      expect(result.contextName).toBe('test-context');
      expect(stateManager.setSessionContext).toHaveBeenCalledWith(
        'test-context',
        expect.objectContaining({
          ...mockContextData,
          _metadata: expect.objectContaining({
            name: 'test-context',
            creator: 'unknown'
          })
        })
      );
    });
    
    it('should save context with custom creator', async () => {
      // Act
      const result = await contextService.saveContext('test-context', mockContextData, {
        creator: 'test-user'
      });
      
      // Assert
      expect(result.success).toBe(true);
      expect(stateManager.setSessionContext).toHaveBeenCalledWith(
        'test-context',
        expect.objectContaining({
          _metadata: expect.objectContaining({
            creator: 'test-user'
          })
        })
      );
    });
    
    it('should throw error for invalid context name', async () => {
      // Act & Assert
      await expect(contextService.saveContext('', mockContextData)).rejects.toThrow('Invalid context name');
      await expect(contextService.saveContext(null, mockContextData)).rejects.toThrow('Invalid context name');
    });
    
    it('should throw error for invalid context data', async () => {
      // Act & Assert
      await expect(contextService.saveContext('test', null)).rejects.toThrow('Context data must be an object');
      await expect(contextService.saveContext('test', 'string')).rejects.toThrow('Context data must be an object');
    });
    
    it('should handle Firebase option', async () => {
      // Act
      const result = await contextService.saveContext('test-context', mockContextData, {
        useFirebase: true
      });
      
      // Assert
      expect(result.success).toBe(true);
      expect(result.storedIn).toBe('local and attempted Firebase');
    });
  });
  
  describe('loadContext', () => {
    beforeEach(() => {
      // Setup context service with some local contexts
      contextService.localContexts = {
        'test-context': {
          ...mockContextData,
          _metadata: {
            name: 'test-context',
            createdAt: new Date().toISOString(),
            creator: 'test'
          }
        }
      };
    });
    
    it('should load context from local storage', async () => {
      // Act
      const result = await contextService.loadContext('test-context');
      
      // Assert
      expect(result.success).toBe(true);
      expect(result.contextName).toBe('test-context');
      expect(result.source).toBe('local');
      expect(result.contextData).toEqual(contextService.localContexts['test-context']);
    });
    
    it('should load context from state manager if not in local storage', async () => {
      // Arrange
      const stateContext = { ...mockContextData, source: 'state' };
      stateManager.getSessionContext.mockReturnValue(stateContext);
      
      // Act
      const result = await contextService.loadContext('state-context');
      
      // Assert
      expect(result.success).toBe(true);
      expect(result.contextName).toBe('state-context');
      expect(result.source).toBe('local');
      expect(result.contextData).toEqual(stateContext);
    });
    
    it('should throw error for non-existent context', async () => {
      // Arrange
      stateManager.getSessionContext.mockReturnValue(null);
      
      // Act & Assert
      await expect(contextService.loadContext('non-existent')).rejects.toThrow('Context non-existent not found');
    });
    
    it('should handle Firebase option', async () => {
      // Act & Assert
      await expect(contextService.loadContext('non-existent', { useFirebase: true }))
        .rejects.toThrow('Context non-existent not found');
    });
  });
  
  describe('listContexts', () => {
    beforeEach(() => {
      // Setup context service with some local contexts
      contextService.localContexts = {
        'local-context-1': mockContextData,
        'local-context-2': mockContextData
      };
      
      stateManager.getState.mockReturnValue({
        sessionContext: {
          'state-context-1': mockContextData,
          'local-context-1': mockContextData // Duplicate
        }
      });
    });
    
    it('should list all available contexts', async () => {
      // Act
      const result = await contextService.listContexts();
      
      // Assert
      expect(result.success).toBe(true);
      expect(result.contexts.local).toEqual(
        expect.arrayContaining(['local-context-1', 'local-context-2', 'state-context-1'])
      );
      expect(result.contexts.local).toHaveLength(3); // No duplicates
      expect(result.contexts.firebase).toEqual([]);
    });
  });
  
  describe('deleteContext', () => {
    beforeEach(() => {
      // Setup context service with some local contexts
      contextService.localContexts = {
        'test-context': mockContextData
      };
      
      stateManager.getState.mockReturnValue({
        sessionContext: {
          'test-context': mockContextData
        }
      });
    });
    
    it('should delete context successfully', async () => {
      // Act
      const result = await contextService.deleteContext('test-context');
      
      // Assert
      expect(result.success).toBe(true);
      expect(result.contextName).toBe('test-context');
      expect(contextService.localContexts['test-context']).toBeUndefined();
    });
    
    it('should handle Firebase deletion option', async () => {
      // Act
      const result = await contextService.deleteContext('test-context', { useFirebase: true });
      
      // Assert
      expect(result.success).toBe(true);
      expect(result.contextName).toBe('test-context');
    });
  });
});
