/**
 * Unit tests for the API Client
 */

const axios = require('axios');
const ApiClient = require('../src/utils/apiClient');

// Mock axios
jest.mock('axios');

describe('ApiClient', () => {
  let apiClient;
  
  beforeEach(() => {
    apiClient = new ApiClient('https://example.com', 'test-api-key');
    jest.clearAllMocks();
  });
  
  describe('getFile', () => {
    it('should fetch file content successfully', async () => {
      // Arrange
      const mockResponse = {
        data: {
          content: 'Test content'
        }
      };
      axios.post.mockResolvedValue(mockResponse);
      
      // Act
      const result = await apiClient.getFile('persona', 'test.md');
      
      // Assert
      expect(axios.post).toHaveBeenCalledWith(
        'https://example.com/file',
        {
          fileType: 'persona',
          filePath: 'test.md'
        },
        {
          headers: { 'X-API-Key': 'test-api-key' }
        }
      );
      expect(result).toBe('Test content');
    });
    
    it('should retry on server error', async () => {
      // Arrange
      const mockError = {
        response: {
          status: 500
        }
      };
      const mockResponse = {
        data: {
          content: 'Test content'
        }
      };
      axios.post.mockRejectedValueOnce(mockError);
      axios.post.mockResolvedValueOnce(mockResponse);
      
      // Act
      const result = await apiClient.getFile('persona', 'test.md');
      
      // Assert
      expect(axios.post).toHaveBeenCalledTimes(2);
      expect(result).toBe('Test content');
    });
    
    it('should throw error after max retries', async () => {
      // Arrange
      const mockError = {
        response: {
          status: 500
        },
        message: 'Server error'
      };
      axios.post.mockRejectedValue(mockError);

      // Act & Assert
      await expect(apiClient.getFile('persona', 'test.md')).rejects.toThrow(
        'Failed to fetch persona/test.md: Server error'
      );
      expect(axios.post).toHaveBeenCalledTimes(4); // Initial + 3 retries
    }, 10000); // Increase timeout to 10 seconds
  });
});