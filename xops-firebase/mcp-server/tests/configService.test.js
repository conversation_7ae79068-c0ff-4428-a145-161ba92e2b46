/**
 * Unit tests for the Config Service
 */

const ConfigService = require('../src/utils/configService');
const ApiClient = require('../src/utils/apiClient');
const stateManager = require('../src/utils/stateManager');

// Mock dependencies
jest.mock('../src/utils/apiClient');
jest.mock('../src/utils/stateManager');

describe('ConfigService', () => {
  let configService;
  let mockApiClient;
  
  const mockConfig = {
    title: 'Test XOps',
    version: '1.0.0',
    dataResolution: {
      personas: 'personas',
      tasks: 'tasks',
      templates: 'templates',
      checklists: 'checklists'
    },
    personas: [
      {
        name: 'test-advisor',
        title: 'Test Advisor',
        description: 'Test persona',
        persona: 'test-advisor.md',
        tasks: [
          {
            name: 'Test Task',
            description: 'A test task',
            taskFile: 'test/test-task.md'
          }
        ]
      }
    ]
  };
  
  beforeEach(() => {
    // Clear all mocks
    jest.clearAllMocks();
    
    // Mock ApiClient
    mockApiClient = {
      getFile: jest.fn()
    };
    ApiClient.mockImplementation(() => mockApiClient);
    
    // Mock stateManager
    stateManager.setConfig = jest.fn();
    stateManager.getConfig = jest.fn();
    
    // Create new instance
    configService = new ConfigService('https://test.com', 'test-key');
  });
  
  describe('loadConfig', () => {
    it('should load and parse configuration successfully', async () => {
      // Arrange
      mockApiClient.getFile.mockResolvedValue(JSON.stringify(mockConfig));
      
      // Act
      const result = await configService.loadConfig();
      
      // Assert
      expect(mockApiClient.getFile).toHaveBeenCalledWith('config', 'config.json');
      expect(stateManager.setConfig).toHaveBeenCalledWith(mockConfig);
      expect(result).toEqual(mockConfig);
    });
    
    it('should throw error for invalid JSON', async () => {
      // Arrange
      mockApiClient.getFile.mockResolvedValue('invalid json');
      
      // Act & Assert
      await expect(configService.loadConfig()).rejects.toThrow('Invalid JSON in config file');
    });
    
    it('should throw error for missing required fields', async () => {
      // Arrange
      const invalidConfig = { title: 'Test' }; // Missing required fields
      mockApiClient.getFile.mockResolvedValue(JSON.stringify(invalidConfig));
      
      // Act & Assert
      await expect(configService.loadConfig()).rejects.toThrow('Missing required configuration field');
    });
    
    it('should use cached config when cache is valid', async () => {
      // Arrange
      mockApiClient.getFile.mockResolvedValue(JSON.stringify(mockConfig));
      
      // Load config first time
      await configService.loadConfig();
      
      // Clear mock calls
      mockApiClient.getFile.mockClear();
      
      // Act - load config again
      const result = await configService.loadConfig();
      
      // Assert
      expect(mockApiClient.getFile).not.toHaveBeenCalled();
      expect(result).toEqual(mockConfig);
    });
    
    it('should force reload when forceReload option is true', async () => {
      // Arrange
      mockApiClient.getFile.mockResolvedValue(JSON.stringify(mockConfig));
      
      // Load config first time
      await configService.loadConfig();
      
      // Clear mock calls
      mockApiClient.getFile.mockClear();
      
      // Act - force reload
      const result = await configService.loadConfig({ forceReload: true });
      
      // Assert
      expect(mockApiClient.getFile).toHaveBeenCalledWith('config', 'config.json');
      expect(result).toEqual(mockConfig);
    });
  });
  
  describe('validateConfig', () => {
    it('should validate valid configuration', () => {
      // Act & Assert
      expect(() => configService.validateConfig(mockConfig)).not.toThrow();
    });
    
    it('should throw error for missing title', () => {
      // Arrange
      const invalidConfig = { ...mockConfig };
      delete invalidConfig.title;
      
      // Act & Assert
      expect(() => configService.validateConfig(invalidConfig)).toThrow('Missing required configuration field: title');
    });
    
    it('should throw error for missing personas', () => {
      // Arrange
      const invalidConfig = { ...mockConfig, personas: [] };
      
      // Act & Assert
      expect(() => configService.validateConfig(invalidConfig)).toThrow('Configuration must contain at least one persona');
    });
    
    it('should throw error for invalid persona structure', () => {
      // Arrange
      const invalidConfig = {
        ...mockConfig,
        personas: [{ name: 'test' }] // Missing required fields
      };
      
      // Act & Assert
      expect(() => configService.validateConfig(invalidConfig)).toThrow('Invalid persona at index 0');
    });
  });
  
  describe('findPersona', () => {
    beforeEach(async () => {
      mockApiClient.getFile.mockResolvedValue(JSON.stringify(mockConfig));
      await configService.loadConfig();
    });
    
    it('should find persona by name', () => {
      // Act
      const result = configService.findPersona('test-advisor');
      
      // Assert
      expect(result).toEqual(mockConfig.personas[0]);
    });
    
    it('should find persona by title', () => {
      // Act
      const result = configService.findPersona('Test Advisor');
      
      // Assert
      expect(result).toEqual(mockConfig.personas[0]);
    });
    
    it('should find persona case-insensitively', () => {
      // Act
      const result = configService.findPersona('TEST-ADVISOR');
      
      // Assert
      expect(result).toEqual(mockConfig.personas[0]);
    });
    
    it('should return null for non-existent persona', () => {
      // Act
      const result = configService.findPersona('non-existent');
      
      // Assert
      expect(result).toBeNull();
    });
  });
  
  describe('getPersonaTasks', () => {
    beforeEach(async () => {
      mockApiClient.getFile.mockResolvedValue(JSON.stringify(mockConfig));
      await configService.loadConfig();
    });
    
    it('should return tasks for existing persona', () => {
      // Act
      const result = configService.getPersonaTasks('test-advisor');
      
      // Assert
      expect(result).toEqual(mockConfig.personas[0].tasks);
    });
    
    it('should return empty array for non-existent persona', () => {
      // Act
      const result = configService.getPersonaTasks('non-existent');
      
      // Assert
      expect(result).toEqual([]);
    });
  });
  
  describe('resolveFilePath', () => {
    beforeEach(async () => {
      mockApiClient.getFile.mockResolvedValue(JSON.stringify(mockConfig));
      await configService.loadConfig();
    });
    
    it('should resolve file path correctly', () => {
      // Act
      const result = configService.resolveFilePath('personas', 'test-advisor');
      
      // Assert
      expect(result).toBe('personas/test-advisor.md');
    });
    
    it('should add extension if not provided', () => {
      // Act
      const result = configService.resolveFilePath('config', 'config');
      
      // Assert
      expect(result).toBe('config/config.json');
    });
    
    it('should throw error for unknown file type', () => {
      // Act & Assert
      expect(() => configService.resolveFilePath('unknown', 'file')).toThrow('Unknown file type: unknown');
    });
  });
  
  describe('getConfigMetadata', () => {
    it('should return not loaded metadata when config not loaded', () => {
      // Act
      const result = configService.getConfigMetadata();
      
      // Assert
      expect(result.loaded).toBe(false);
    });
    
    it('should return loaded metadata when config is loaded', async () => {
      // Arrange
      mockApiClient.getFile.mockResolvedValue(JSON.stringify(mockConfig));
      await configService.loadConfig();
      
      // Act
      const result = configService.getConfigMetadata();
      
      // Assert
      expect(result.loaded).toBe(true);
      expect(result.title).toBe(mockConfig.title);
      expect(result.version).toBe(mockConfig.version);
      expect(result.personaCount).toBe(mockConfig.personas.length);
    });
  });
});
