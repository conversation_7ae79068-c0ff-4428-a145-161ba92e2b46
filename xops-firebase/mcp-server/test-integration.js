/**
 * XOps MCP Server Integration Test Script
 * 
 * This script tests the integration between the MCP Server and Firebase
 * by calling each tool endpoint and verifying the responses.
 */

require('dotenv').config();
const axios = require('axios');
const colors = require('colors/safe');

// Configuration
const BASE_URL = `http://localhost:${process.env.PORT || 3001}`;
const TEST_PERSONA = 'developer'; // Change to a persona that exists in your Firebase storage
const TEST_TEMPLATE = 'react-component'; // Change to a template that exists in your Firebase storage
const TEST_TASK = 'create-component'; // Change to a task that exists in your Firebase storage
const TEST_CHECKLIST = 'code-review'; // Change to a checklist that exists in your Firebase storage

// Test document content for checklist test
const TEST_DOCUMENT = `
# React Component

This is a sample React component.

\`\`\`jsx
import React from 'react';

const MyComponent = ({ name }) => {
  return <div>Hello, {name}!</div>;
};

export default MyComponent;
\`\`\`
`;

/**
 * Run all tests
 */
async function runTests() {
  console.log(colors.cyan('=== XOps MCP Server Integration Tests ===\n'));
  
  try {
    // Test 1: Check server health
    await testServerHealth();
    
    // Test 2: Test change-persona tool
    await testChangePerson();
    
    // Test 3: Test fetch-template tool
    await testFetchTemplate();
    
    // Test 4: Test load-task tool
    await testLoadTask();
    
    // Test 5: Test run-checklist tool
    await testRunChecklist();
    
    // Test 6: Test save-context tool
    await testSaveContext();

    // Test 7: Test load-context tool
    await testLoadContext();

    // Test 8: Test get-config tool
    await testGetConfig();

    // Test 9: Test clear-state tool
    await testClearState();
    
    console.log(colors.green('\nAll tests completed successfully! ✅'));
  } catch (error) {
    console.error(colors.red('\nTests failed with error:'), error.message);
    process.exit(1);
  }
}

/**
 * Test server health
 */
async function testServerHealth() {
  console.log(colors.yellow('Testing server health...'));
  
  try {
    const response = await axios.get(`${BASE_URL}/health`);
    
    if (response.data.status === 'ok') {
      console.log(colors.green('✅ Server is healthy'));
    } else {
      throw new Error('Server health check failed');
    }
  } catch (error) {
    console.error(colors.red('❌ Server health check failed:'), error.message);
    throw error;
  }
}

/**
 * Test change-persona tool
 */
async function testChangePerson() {
  console.log(colors.yellow(`\nTesting change-persona tool with persona: ${TEST_PERSONA}...`));
  
  try {
    const response = await axios.post(`${BASE_URL}/tools/change-persona`, {
      params: {
        personaName: TEST_PERSONA
      }
    });
    
    if (response.data.result && response.data.result.success) {
      console.log(colors.green(`✅ Successfully changed persona to ${TEST_PERSONA}`));
      console.log(colors.gray(`Status bar: ${response.data.statusBar?.text || 'N/A'}`));
    } else {
      throw new Error('Failed to change persona');
    }
  } catch (error) {
    console.error(colors.red('❌ change-persona test failed:'), error.message);
    throw error;
  }
}

/**
 * Test fetch-template tool
 */
async function testFetchTemplate() {
  console.log(colors.yellow(`\nTesting fetch-template tool with template: ${TEST_TEMPLATE}...`));
  
  try {
    const response = await axios.post(`${BASE_URL}/tools/fetch-template`, {
      params: {
        templateName: TEST_TEMPLATE
      }
    });
    
    if (response.data.result && response.data.result.success) {
      console.log(colors.green(`✅ Successfully fetched template ${TEST_TEMPLATE}`));
      console.log(colors.gray(`Status bar: ${response.data.statusBar?.text || 'N/A'}`));
      console.log(colors.gray(`Content preview: ${response.data.result.content.substring(0, 50)}...`));
    } else {
      throw new Error('Failed to fetch template');
    }
  } catch (error) {
    console.error(colors.red('❌ fetch-template test failed:'), error.message);
    throw error;
  }
}

/**
 * Test load-task tool
 */
async function testLoadTask() {
  console.log(colors.yellow(`\nTesting load-task tool with task: ${TEST_TASK}...`));
  
  try {
    const response = await axios.post(`${BASE_URL}/tools/load-task`, {
      params: {
        taskName: TEST_TASK
      }
    });
    
    if (response.data.result && response.data.result.success) {
      console.log(colors.green(`✅ Successfully loaded task ${TEST_TASK}`));
      console.log(colors.gray(`Status bar: ${response.data.statusBar?.text || 'N/A'}`));
      console.log(colors.gray(`Content preview: ${response.data.result.content.substring(0, 50)}...`));
    } else {
      throw new Error('Failed to load task');
    }
  } catch (error) {
    console.error(colors.red('❌ load-task test failed:'), error.message);
    throw error;
  }
}

/**
 * Test run-checklist tool
 */
async function testRunChecklist() {
  console.log(colors.yellow(`\nTesting run-checklist tool with checklist: ${TEST_CHECKLIST}...`));
  
  try {
    const response = await axios.post(`${BASE_URL}/tools/run-checklist`, {
      params: {
        checklistName: TEST_CHECKLIST,
        documentContent: TEST_DOCUMENT
      }
    });
    
    if (response.data.result && response.data.result.success) {
      console.log(colors.green(`✅ Successfully ran checklist ${TEST_CHECKLIST}`));
      console.log(colors.gray(`Status bar: ${response.data.statusBar?.text || 'N/A'}`));
      console.log(colors.gray(`Results: ${response.data.result.results.passed ? 'Passed' : 'Failed'}`));
    } else {
      throw new Error('Failed to run checklist');
    }
  } catch (error) {
    console.error(colors.red('❌ run-checklist test failed:'), error.message);
    throw error;
  }
}

/**
 * Test save-context tool
 */
async function testSaveContext() {
  console.log(colors.yellow('\nTesting save-context tool...'));
  
  try {
    const contextData = {
      testKey: 'testValue',
      timestamp: new Date().toISOString()
    };
    
    const response = await axios.post(`${BASE_URL}/tools/save-context`, {
      params: {
        contextName: 'testContext',
        contextData: contextData
      }
    });
    
    if (response.data.result && response.data.result.success) {
      console.log(colors.green('✅ Successfully saved context'));
      console.log(colors.gray(`Status bar: ${response.data.statusBar?.text || 'N/A'}`));
    } else {
      throw new Error('Failed to save context');
    }
  } catch (error) {
    console.error(colors.red('❌ save-context test failed:'), error.message);
    throw error;
  }
}

/**
 * Test load-context tool
 */
async function testLoadContext() {
  console.log(colors.yellow('\nTesting load-context tool...'));

  try {
    const response = await axios.post(`${BASE_URL}/tools/load-context`, {
      params: {
        contextName: 'testContext'
      }
    });

    if (response.data.result && response.data.result.success) {
      console.log(colors.green('✅ Successfully loaded context'));
      console.log(colors.gray(`Status bar: ${response.data.statusBar?.text || 'N/A'}`));
      console.log(colors.gray(`Source: ${response.data.result.source || 'N/A'}`));
    } else {
      throw new Error('Failed to load context');
    }
  } catch (error) {
    console.error(colors.red('❌ load-context test failed:'), error.message);
    throw error;
  }
}

/**
 * Test get-config tool
 */
async function testGetConfig() {
  console.log(colors.yellow('\nTesting get-config tool...'));

  try {
    const response = await axios.get(`${BASE_URL}/tools/get-config`);

    if (response.data.result && response.data.result.success) {
      console.log(colors.green('✅ Successfully retrieved configuration'));
      console.log(colors.gray(`Status bar: ${response.data.statusBar?.text || 'N/A'}`));
      console.log(colors.gray(`Config title: ${response.data.result.config?.title || 'N/A'}`));
      console.log(colors.gray(`Config version: ${response.data.result.config?.version || 'N/A'}`));
    } else {
      throw new Error('Failed to get configuration');
    }
  } catch (error) {
    console.error(colors.red('❌ get-config test failed:'), error.message);
    throw error;
  }
}

/**
 * Test clear-state tool
 */
async function testClearState() {
  console.log(colors.yellow('\nTesting clear-state tool...'));

  try {
    const response = await axios.post(`${BASE_URL}/tools/clear-state`);

    if (response.data.result && response.data.result.success) {
      console.log(colors.green('✅ Successfully cleared state'));
      console.log(colors.gray(`Status bar: ${response.data.statusBar?.text || 'N/A'}`));
    } else {
      throw new Error('Failed to clear state');
    }
  } catch (error) {
    console.error(colors.red('❌ clear-state test failed:'), error.message);
    throw error;
  }
}

// Run the tests
runTests();