# Continue.dev Integration Setup Guide

This guide provides detailed instructions for setting up Continue.dev to work with the enhanced XOps MCP Server.

## Server Status

The XOps MCP Server is **production-ready** with the following features:
- ✅ All 9 tools implemented and tested (35 passing tests)
- ✅ Config-driven authority with validation
- ✅ Enhanced error handling with user-friendly messages
- ✅ State management with persistence and metadata
- ✅ Context management with local and Firebase storage
- ✅ Comprehensive API documentation
- ✅ Professional deployment configuration

## Prerequisites

- XOps MCP Server installed and running (see INSTALL.md)
- Continue.dev extension installed in your IDE (VS Code, JetBrains, etc.)
- Node.js 18+ and npm installed
- Valid Firebase configuration with config.j<PERSON> uploaded (for full functionality)

## Step 1: Install Continue.dev

### For VS Code
1. Open VS Code
2. Go to the Extensions view (Ctrl+Shift+X / Cmd+Shift+X)
3. Search for "Continue"
4. Install the "Continue" extension by Continue Dev
5. Reload VS Code when prompted

### For JetBrains IDEs
1. Open your JetBrains IDE (IntelliJ, WebStorm, etc.)
2. Go to Settings/Preferences
3. Navigate to Plugins
4. Search for "Continue"
5. Install the "Continue" plugin by Continue Dev
6. Restart the IDE when prompted

## Step 2: Configure Continue.dev

### Access Continue.dev Configuration

#### VS Code
1. Open VS Code
2. Open Command Palette (Ctrl+Shift+P / Cmd+Shift+P)
3. Type and select "Continue: Open Config"

#### JetBrains IDEs
1. Open your JetBrains IDE
2. Go to Tools → Continue → Open Config

### Update Configuration

Add the XOps MCP Server configuration to your Continue.dev config file:

```json
{
  "models": [
    {
      "title": "XOps Orchestrator",
      "provider": "anthropic",
      "model": "claude-3-opus-20240229",
      "completionOptions": {
        "tools": [
          {
            "name": "change-persona",
            "description": "Change the active AI persona",
            "parameters": {
              "type": "object",
              "properties": {
                "personaName": {
                  "type": "string",
                  "description": "Name of the persona to activate"
                }
              },
              "required": ["personaName"]
            },
            "handler": {
              "type": "http",
              "url": "http://localhost:3001/tools/change-persona"
            }
          },
          {
            "name": "fetch-template",
            "description": "Fetch a template for a task",
            "parameters": {
              "type": "object",
              "properties": {
                "templateName": {
                  "type": "string",
                  "description": "Name of the template to fetch"
                }
              },
              "required": ["templateName"]
            },
            "handler": {
              "type": "http",
              "url": "http://localhost:3001/tools/fetch-template"
            }
          },
          {
            "name": "run-checklist",
            "description": "Run a checklist against the current document",
            "parameters": {
              "type": "object",
              "properties": {
                "checklistName": {
                  "type": "string",
                  "description": "Name of the checklist to run"
                },
                "documentContent": {
                  "type": "string",
                  "description": "Content of the document to check"
                }
              },
              "required": ["checklistName", "documentContent"]
            },
            "handler": {
              "type": "http",
              "url": "http://localhost:3001/tools/run-checklist"
            }
          },
          {
            "name": "load-task",
            "description": "Load a specific task workflow",
            "parameters": {
              "type": "object",
              "properties": {
                "taskName": {
                  "type": "string",
                  "description": "Name of the task to load"
                }
              },
              "required": ["taskName"]
            },
            "handler": {
              "type": "http",
              "url": "http://localhost:3001/tools/load-task"
            }
          },
          {
            "name": "save-context",
            "description": "Save the current context for later use",
            "parameters": {
              "type": "object",
              "properties": {
                "contextName": {
                  "type": "string",
                  "description": "Name to save the context under"
                },
                "contextData": {
                  "type": "object",
                  "description": "Context data to save"
                }
              },
              "required": ["contextName", "contextData"]
            },
            "handler": {
              "type": "http",
              "url": "http://localhost:3001/tools/save-context"
            }
          },
          {
            "name": "load-context",
            "description": "Load a previously saved context",
            "parameters": {
              "type": "object",
              "properties": {
                "contextName": {
                  "type": "string",
                  "description": "Name of the context to load"
                },
                "useFirebase": {
                  "type": "boolean",
                  "description": "Whether to try loading from Firebase",
                  "default": false
                }
              },
              "required": ["contextName"]
            },
            "handler": {
              "type": "http",
              "url": "http://localhost:3001/tools/load-context"
            }
          },
          {
            "name": "get-config",
            "description": "Get the current XOps configuration",
            "parameters": {
              "type": "object",
              "properties": {}
            },
            "handler": {
              "type": "http",
              "url": "http://localhost:3001/tools/get-config"
            }
          },
          {
            "name": "clear-state",
            "description": "Clear the current state of the agent",
            "parameters": {
              "type": "object",
              "properties": {}
            },
            "handler": {
              "type": "http",
              "url": "http://localhost:3001/tools/clear-state"
            }
          }
        ]
      }
    }
  ]
}
```

You can copy this configuration from the provided `continue-config.json` file in the XOps MCP Server directory.

## Step 3: Set Up API Keys

### Configure API Keys in Continue.dev

1. Open Continue.dev settings
2. Navigate to the "API Keys" section
3. Add your API key for Anthropic (Claude)

## Step 4: Test the Integration

### Start the MCP Server

Before testing, make sure the MCP Server is running:

```bash
cd xops-firebase/mcp-server
npm start
```

### Verify Server Health

Open a browser and navigate to:
http://localhost:3001/health

You should see a response indicating the server is healthy.

### Test with Continue.dev

1. Open a coding project in your IDE
2. Open Continue.dev chat (VS Code: Ctrl+Shift+L / Cmd+Shift+L)
3. Select "XOps Orchestrator" as the model
4. Try using one of the tools, for example:

```
Change to the developer persona for me.
```

The AI should use the change-persona tool to switch to the developer persona.

## Step 5: Using XOps Tools in Continue.dev

Here are examples of how to use each tool:

### Change Persona

Ask the AI to switch to a specific persona:

```
Switch to the architect persona.
```

### Fetch Template

Ask the AI to fetch a template:

```
Get me the React component template.
```

### Run Checklist

Ask the AI to run a checklist against a document:

```
Run the code review checklist on this file.
```

### Load Task

Ask the AI to load a specific task:

```
I want to create a new component. Load the create component task.
```

### Save Context

Ask the AI to save the current context:

```text
Save this discussion as "component-design".
```

### Load Context

Ask the AI to load a previously saved context:

```text
Load the "component-design" context we saved earlier.
```

### Get Configuration

Ask the AI to retrieve the current configuration:

```text
Show me the current XOps configuration and available personas.
```

### Clear State

Ask the AI to clear its state:

```text
Clear your state and start fresh.
```

## Troubleshooting

### Connection Issues

If Continue.dev cannot connect to the MCP Server:

1. **Verify Server Status**: Check that the server is running on port 3001
   ```bash
   curl http://localhost:3001/health
   ```
   Expected response: `{"status":"ok","timestamp":"...","server":"XOps MCP Server"}`

2. **Check Server Logs**: Look for any startup errors in the server console
3. **Verify Configuration**: Ensure the URL in Continue.dev matches `http://localhost:3001/tools/[tool-name]`
4. **Firewall Settings**: Make sure your firewall allows connections to localhost:3001

### Configuration Issues

If the configuration doesn't load properly:

1. **Check Firebase Configuration**: Verify that `config.json` exists in Firebase Storage
2. **Validate Configuration**: Use the get-config tool to check configuration status
3. **Check Environment Variables**: Ensure `.env` file has correct Firebase endpoint and API key
4. **Review Server Logs**: Look for configuration loading errors in the console

### Tool Execution Issues

If tools don't work as expected:

1. **Check Tool Status**: Use the `/tools` endpoint to verify all tools are available
2. **Review Error Messages**: The enhanced error handling provides detailed error information
3. **Validate Parameters**: Ensure tool parameters match the expected format
4. **Check State**: Use the state tool to verify current server state
5. **Test Locally**: Use curl commands to test tools directly

### Context Management Issues

If context saving/loading fails:

1. **Local Storage**: Context is saved locally by default and should always work
2. **Firebase Storage**: If using Firebase option, check Firebase connectivity
3. **State Verification**: Use the state tool to see saved contexts
4. **Clear and Retry**: Use clear-state tool to reset and try again

### Performance Issues

If the server responds slowly:

1. **Check System Resources**: Monitor CPU and memory usage
2. **Firebase Latency**: Network issues may affect Firebase API calls
3. **Configuration Cache**: The config service caches configuration for 5 minutes
4. **Restart Server**: Restart the MCP server if performance degrades

## Additional Resources

- [Continue.dev Documentation](https://continue.dev/docs)
- [XOps MCP Server README](/xops-firebase/mcp-server/README.md)
- [XOps Firebase Integration Plan](/xops-firebase/docs/xops-firebase-integration-plan.md)