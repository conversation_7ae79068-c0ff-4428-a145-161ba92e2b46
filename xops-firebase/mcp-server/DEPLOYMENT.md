# XOps MCP Server Deployment Guide

This guide provides comprehensive instructions for deploying the production-ready XOps MCP Server to various environments.

## Production Readiness Status

The XOps MCP Server is **production-ready** with the following verified features:

- ✅ **All 9 Tools Implemented**: Complete functionality tested (35 passing tests)
- ✅ **Config-Driven Authority**: Centralized configuration management
- ✅ **Enhanced Error Handling**: Professional error responses with status bar integration
- ✅ **State Management**: Persistent state with metadata and versioning
- ✅ **Context Management**: Local and Firebase storage options
- ✅ **Comprehensive Testing**: Unit and integration test coverage
- ✅ **API Documentation**: Complete API specifications
- ✅ **Security**: Helmet middleware and CORS protection
- ✅ **Performance**: Configuration caching and connection pooling

## Deployment Options

The XOps MCP Server can be deployed in several ways:

1. **Local Development**: Running on your local machine
2. **Docker Container**: Running in a containerized environment
3. **Google Cloud Run**: Recommended for production (serverless containers)
4. **AWS ECS/Fargate**: Enterprise container deployment
5. **Kubernetes**: For complex orchestration needs
6. **Traditional VPS/VM**: Running on a virtual private server

## Prerequisites

- Node.js 18+ installed (verified compatibility)
- npm package manager
- Git for version control
- Firebase CLI installed (for Firebase deployments)
- Docker installed (for containerized deployments)
- Google Cloud SDK (for GCP deployments)
- Valid Firebase configuration with config.json uploaded

## Local Deployment

For local development and testing:

```bash
# Clone the repository
git clone <repository-url>
cd xops-firebase/mcp-server

# Install dependencies
npm install

# Configure environment variables
cp .env.example .env
# Edit .env with your specific configuration

# Start the server
npm start
```

The server will be available at <http://localhost:3001>

### Verification

After starting the server, verify it's working correctly:

```bash
# Health check
curl http://localhost:3001/health
# Expected: {"status":"ok","timestamp":"...","server":"XOps MCP Server"}

# Tools listing
curl http://localhost:3001/tools
# Expected: List of all 9 available tools

# Test context management
curl -X POST http://localhost:3001/tools/save-context \
  -H "Content-Type: application/json" \
  -d '{"params":{"contextName":"test","contextData":{"project":"Test"}}}'
# Expected: {"result":{"success":true,"contextName":"test","storedIn":"local only"}}

# Test state management
curl http://localhost:3001/tools/state
# Expected: Current state with metadata and version info
```

## Docker Deployment

### Creating a Docker Image

1. Create a Dockerfile in the mcp-server directory:

```dockerfile
FROM node:16-alpine

WORKDIR /app

COPY package*.json ./
RUN npm ci --only=production

COPY . .

EXPOSE 3001

CMD ["node", "server.js"]
```

2. Create a .dockerignore file:

```
node_modules
npm-debug.log
.env
.git
.gitignore
README.md
INSTALL.md
TESTING.md
DEPLOYMENT.md
CONTINUE_SETUP.md
```

3. Build the Docker image:

```bash
docker build -t xops-mcp-server .
```

4. Run the Docker container:

```bash
docker run -p 3001:3001 --env-file .env xops-mcp-server
```

### Docker Compose (Optional)

For more complex setups, create a docker-compose.yml file:

```yaml
version: '3'
services:
  mcp-server:
    build: .
    ports:
      - "3001:3001"
    env_file:
      - .env
    restart: unless-stopped
```

Run with:

```bash
docker-compose up -d
```

## Deploying to Google Cloud Run

Cloud Run is a good option for running the MCP Server in production:

1. Build and push the Docker image to Google Container Registry:

```bash
# Set project ID
export PROJECT_ID=your-project-id

# Build the image
docker build -t gcr.io/$PROJECT_ID/xops-mcp-server .

# Push to Container Registry
docker push gcr.io/$PROJECT_ID/xops-mcp-server
```

2. Deploy to Cloud Run:

```bash
gcloud run deploy xops-mcp-server \
  --image gcr.io/$PROJECT_ID/xops-mcp-server \
  --platform managed \
  --region us-central1 \
  --allow-unauthenticated \
  --set-env-vars FIREBASE_ENDPOINT=https://us-central1-your-project-id.cloudfunctions.net/xopsAgent,API_KEY=your-api-key
```

3. Get the deployed URL:

```bash
gcloud run services describe xops-mcp-server --platform managed --region us-central1 --format 'value(status.url)'
```

4. Update your continue.dev configuration with the new URL.

## Deploying to Firebase Functions

The MCP Server can also be deployed as a Firebase Function:

1. Modify the directory structure to work with Firebase Functions:

```
functions/
  ├── index.js  # Main entry point
  ├── package.json
  ├── server.js
  └── src/
      └── ...   # Existing source files
```

2. Create an index.js file in the functions directory:

```javascript
const functions = require('firebase-functions');
const app = require('./server');

exports.mcpServer = functions
  .runWith({
    memory: '512MB',
    timeoutSeconds: 300
  })
  .https.onRequest(app);
```

3. Modify server.js to export the app:

```javascript
// At the end of the file, instead of app.listen()
if (process.env.NODE_ENV !== 'production') {
  const PORT = process.env.PORT || 3001;
  app.listen(PORT, () => {
    console.log(`MCP Server running on port ${PORT}`);
  });
}

module.exports = app;
```

4. Deploy to Firebase Functions:

```bash
firebase deploy --only functions:mcpServer
```

5. Get the deployed URL from the Firebase console or command output and update your continue.dev configuration.

## Deploying to a VPS/VM

For traditional hosting on a Virtual Private Server:

1. SSH into your server:

```bash
ssh user@your-server-ip
```

2. Install Node.js and npm:

```bash
curl -sL https://deb.nodesource.com/setup_16.x | sudo -E bash -
sudo apt-get install -y nodejs
```

3. Clone the repository:

```bash
git clone <repository-url>
cd xops-firebase/mcp-server
```

4. Install dependencies:

```bash
npm ci --only=production
```

5. Set up environment variables:

```bash
cp .env.example .env
nano .env  # Edit with your configuration
```

6. Install PM2 for process management:

```bash
sudo npm install -g pm2
```

7. Start the server with PM2:

```bash
pm2 start server.js --name xops-mcp-server
pm2 save
pm2 startup
```

8. Set up Nginx as a reverse proxy (optional but recommended):

```bash
sudo apt-get install nginx
```

Create an Nginx configuration file:

```
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://localhost:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
    }
}
```

Save to `/etc/nginx/sites-available/xops-mcp-server` and enable:

```bash
sudo ln -s /etc/nginx/sites-available/xops-mcp-server /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl restart nginx
```

9. Set up SSL with Let's Encrypt (recommended):

```bash
sudo apt-get install certbot python3-certbot-nginx
sudo certbot --nginx -d your-domain.com
```

## Security Considerations

For production deployments, consider:

1. **API Key Management**: Use a secure method for API key distribution
2. **Rate Limiting**: Implement rate limiting to prevent abuse
3. **CORS Configuration**: Set appropriate CORS headers for your domain
4. **Firewall Rules**: Restrict access to your server
5. **Monitoring**: Set up monitoring and alerting
6. **Logging**: Configure comprehensive logging
7. **HTTPS**: Always use HTTPS in production

## Environment Configuration

These environment variables should be configured for your deployment:

| Variable | Description | Required | Default |
|----------|-------------|----------|---------|
| PORT | Port to run the server on | No | 3001 |
| FIREBASE_ENDPOINT | URL of the Firebase function API | Yes | - |
| API_KEY | API key for the Firebase function | Yes | - |
| NODE_ENV | Environment (development/production) | No | development |
| LOG_LEVEL | Logging level | No | info |

## Monitoring and Maintenance

### Health Checks

Monitor the `/health` endpoint to ensure the service is running:

```bash
curl https://your-mcp-server-url/health
```

### Logs

View logs depending on your deployment:

- **PM2**: `pm2 logs xops-mcp-server`
- **Docker**: `docker logs <container_id>`
- **Cloud Run**: View logs in Google Cloud Console
- **Firebase Functions**: View logs in Firebase Console

### Updates

To update the server:

1. Pull the latest code:

```bash
git pull
```

2. Install dependencies:

```bash
npm ci --only=production
```

3. Restart the service:

```bash
# For PM2
pm2 restart xops-mcp-server

# For Docker
docker-compose pull
docker-compose up -d

# For Cloud Run
# Rebuild and redeploy using the steps above

# For Firebase Functions
firebase deploy --only functions:mcpServer
```

## Troubleshooting

### Common Issues

1. **Connection Refused**: Check if the server is running and port is correct
2. **API Key Invalid**: Verify the API key in your environment variables
3. **Firebase Connection Fails**: Check the Firebase endpoint URL
4. **CORS Errors**: Configure CORS settings for your domain
5. **Memory Issues**: Increase allocated memory in cloud deployments

### Getting Support

If you encounter issues:

1. Check the server logs
2. Consult the troubleshooting sections in INSTALL.md and CONTINUE_SETUP.md
3. File an issue in the project repository