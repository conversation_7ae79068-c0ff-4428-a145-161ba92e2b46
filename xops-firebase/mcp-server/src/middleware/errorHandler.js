/**
 * Enhanced Error Handling Middleware
 * 
 * This middleware provides comprehensive error handling for the MCP server,
 * including specific error types, logging, and user-friendly error messages.
 */

const colors = require('colors');

/**
 * Error types for categorization
 */
const ErrorTypes = {
  VALIDATION: 'VALIDATION_ERROR',
  AUTHENTICATION: 'AUTHENTICATION_ERROR',
  AUTHORIZATION: 'AUTHORIZATION_ERROR',
  NOT_FOUND: 'NOT_FOUND_ERROR',
  EXTERNAL_API: 'EXTERNAL_API_ERROR',
  CONFIG: 'CONFIG_ERROR',
  CONTEXT: 'CONTEXT_ERROR',
  INTERNAL: 'INTERNAL_ERROR'
};

/**
 * Custom error class for MCP server errors
 */
class MCPError extends Error {
  constructor(message, type = ErrorTypes.INTERNAL, statusCode = 500, details = null) {
    super(message);
    this.name = 'MCPError';
    this.type = type;
    this.statusCode = statusCode;
    this.details = details;
    this.timestamp = new Date().toISOString();
  }
}

/**
 * Create specific error types
 */
const createValidationError = (message, details = null) => 
  new MCPError(message, ErrorTypes.VALIDATION, 400, details);

const createNotFoundError = (message, details = null) => 
  new MCPError(message, ErrorTypes.NOT_FOUND, 404, details);

const createConfigError = (message, details = null) => 
  new MCPError(message, ErrorTypes.CONFIG, 500, details);

const createContextError = (message, details = null) => 
  new MCPError(message, ErrorTypes.CONTEXT, 500, details);

const createExternalApiError = (message, details = null) => 
  new MCPError(message, ErrorTypes.EXTERNAL_API, 502, details);

/**
 * Enhanced error handler middleware
 */
const errorHandler = (err, req, res, next) => {
  // Log the error
  logError(err, req);
  
  // Determine error type and response
  let errorResponse;
  
  if (err instanceof MCPError) {
    errorResponse = handleMCPError(err);
  } else if (err.name === 'ValidationError') {
    errorResponse = handleValidationError(err);
  } else if (err.code === 'ECONNREFUSED' || err.code === 'ENOTFOUND') {
    errorResponse = handleConnectionError(err);
  } else if (err.response && err.response.status) {
    errorResponse = handleHttpError(err);
  } else {
    errorResponse = handleGenericError(err);
  }
  
  // Add request context
  errorResponse.requestId = req.headers['x-request-id'] || generateRequestId();
  errorResponse.timestamp = new Date().toISOString();
  errorResponse.path = req.path;
  errorResponse.method = req.method;
  
  // Send error response
  res.status(errorResponse.statusCode).json(errorResponse);
};

/**
 * Handle MCP-specific errors
 */
function handleMCPError(err) {
  return {
    error: err.message,
    type: err.type,
    statusCode: err.statusCode,
    details: err.details,
    statusBar: {
      text: getStatusBarMessage(err),
      icon: getStatusBarIcon(err.type)
    }
  };
}

/**
 * Handle validation errors
 */
function handleValidationError(err) {
  return {
    error: 'Validation failed',
    type: ErrorTypes.VALIDATION,
    statusCode: 400,
    details: err.errors || err.message,
    statusBar: {
      text: 'Validation error',
      icon: '⚠️'
    }
  };
}

/**
 * Handle connection errors
 */
function handleConnectionError(err) {
  return {
    error: 'External service unavailable',
    type: ErrorTypes.EXTERNAL_API,
    statusCode: 503,
    details: 'Unable to connect to Firebase API',
    statusBar: {
      text: 'Service unavailable',
      icon: '🔌'
    }
  };
}

/**
 * Handle HTTP errors from external APIs
 */
function handleHttpError(err) {
  const status = err.response.status;
  const data = err.response.data;
  
  return {
    error: data?.error || 'External API error',
    type: ErrorTypes.EXTERNAL_API,
    statusCode: status >= 500 ? 502 : status,
    details: data?.details || `HTTP ${status} error`,
    statusBar: {
      text: `API error: ${status}`,
      icon: '🌐'
    }
  };
}

/**
 * Handle generic errors
 */
function handleGenericError(err) {
  return {
    error: 'Internal server error',
    type: ErrorTypes.INTERNAL,
    statusCode: 500,
    details: process.env.NODE_ENV === 'development' ? err.message : 'An unexpected error occurred',
    statusBar: {
      text: 'Internal error',
      icon: '❌'
    }
  };
}

/**
 * Get status bar message based on error
 */
function getStatusBarMessage(err) {
  switch (err.type) {
    case ErrorTypes.VALIDATION:
      return `Validation error: ${err.message}`;
    case ErrorTypes.NOT_FOUND:
      return `Not found: ${err.message}`;
    case ErrorTypes.CONFIG:
      return `Config error: ${err.message}`;
    case ErrorTypes.CONTEXT:
      return `Context error: ${err.message}`;
    case ErrorTypes.EXTERNAL_API:
      return `API error: ${err.message}`;
    default:
      return `Error: ${err.message}`;
  }
}

/**
 * Get status bar icon based on error type
 */
function getStatusBarIcon(type) {
  switch (type) {
    case ErrorTypes.VALIDATION:
      return '⚠️';
    case ErrorTypes.NOT_FOUND:
      return '🔍';
    case ErrorTypes.CONFIG:
      return '⚙️';
    case ErrorTypes.CONTEXT:
      return '📂';
    case ErrorTypes.EXTERNAL_API:
      return '🌐';
    default:
      return '❌';
  }
}

/**
 * Log error with context
 */
function logError(err, req) {
  const timestamp = new Date().toISOString();
  const requestId = req.headers['x-request-id'] || generateRequestId();
  
  console.error(colors.red(`[${timestamp}] Error ${requestId}:`));
  console.error(colors.red(`  Method: ${req.method}`));
  console.error(colors.red(`  Path: ${req.path}`));
  console.error(colors.red(`  Message: ${err.message}`));
  
  if (err.stack && process.env.NODE_ENV === 'development') {
    console.error(colors.gray(`  Stack: ${err.stack}`));
  }
  
  if (req.body && Object.keys(req.body).length > 0) {
    console.error(colors.gray(`  Body: ${JSON.stringify(req.body, null, 2)}`));
  }
}

/**
 * Generate a simple request ID
 */
function generateRequestId() {
  return Math.random().toString(36).substr(2, 9);
}

/**
 * Async error wrapper for route handlers
 */
const asyncHandler = (fn) => (req, res, next) => {
  Promise.resolve(fn(req, res, next)).catch(next);
};

module.exports = {
  errorHandler,
  asyncHandler,
  MCPError,
  ErrorTypes,
  createValidationError,
  createNotFoundError,
  createConfigError,
  createContextError,
  createExternalApiError
};
