const express = require('express');
const ApiClient = require('../utils/apiClient');
const stateManager = require('../utils/stateManager');
const contextService = require('../utils/contextService');
const configService = require('../utils/configService');

const router = express.Router();
const apiClient = new ApiClient(
  process.env.FIREBASE_ENDPOINT,
  process.env.API_KEY
);

// GET handler for /tools to provide helpful information
router.get('/', (req, res) => {
  res.json({
    message: 'XOps MCP Server Tools API',
    description: 'This API provides tools for continue.dev integration',
    availableTools: [
      {
        name: 'change-persona',
        method: 'POST',
        endpoint: '/tools/change-persona',
        description: 'Change the active AI persona',
        parameters: { personaName: 'string' }
      },
      {
        name: 'fetch-template',
        method: 'POST',
        endpoint: '/tools/fetch-template',
        description: 'Fetch a template for a task',
        parameters: { templateName: 'string' }
      },
      {
        name: 'run-checklist',
        method: 'POST',
        endpoint: '/tools/run-checklist',
        description: 'Run a checklist against a document',
        parameters: { checklistName: 'string', documentContent: 'string' }
      },
      {
        name: 'load-task',
        method: 'POST',
        endpoint: '/tools/load-task',
        description: 'Load a specific task workflow',
        parameters: { taskName: 'string' }
      },
      {
        name: 'save-context',
        method: 'POST',
        endpoint: '/tools/save-context',
        description: 'Save the current context for later use',
        parameters: { contextName: 'string', contextData: 'object' }
      },
      {
        name: 'load-context',
        method: 'POST',
        endpoint: '/tools/load-context',
        description: 'Load a previously saved context',
        parameters: { contextName: 'string' }
      },
      {
        name: 'get-config',
        method: 'GET',
        endpoint: '/tools/get-config',
        description: 'Get the current configuration',
        parameters: {}
      }
    ],
    debugTools: [
      {
        name: 'state',
        method: 'GET',
        endpoint: '/tools/state',
        description: 'Get the current state'
      },
      {
        name: 'clear-state',
        method: 'POST',
        endpoint: '/tools/clear-state',
        description: 'Clear the current state'
      }
    ],
    usage: 'This API is designed to be used with continue.dev tools integration'
  });
});

// Change persona tool
router.post('/change-persona', async (req, res) => {
  try {
    console.log('Received change-persona request:', req.body);
    const { personaName } = req.body.params || {};

    if (!personaName) {
      return res.status(400).json({
        error: 'Missing required parameter: personaName',
        statusBar: {
          text: `Error: Missing persona name`,
          icon: '⚠️'
        }
      });
    }

    console.log(`Changing persona to: ${personaName}`);

    // Load config if not already loaded
    let config = configService.getConfig();
    if (!config) {
      config = await configService.loadConfig();
    }

    // Find persona in config (config-driven authority)
    const persona = configService.findPersona(personaName);
    if (!persona) {
      const availablePersonas = configService.getPersonas().map(p => p.name);
      return res.status(400).json({
        error: `Persona "${personaName}" not found in configuration`,
        availablePersonas: availablePersonas,
        statusBar: {
          text: `Error: Persona "${personaName}" not found`,
          icon: '⚠️'
        }
      });
    }

    // Fetch persona content from Firebase using config-resolved path
    const dataResolution = configService.getDataResolution();
    const personaPath = `${dataResolution.personas}/${persona.persona}`;
    const content = await apiClient.getFile('persona', persona.persona);

    // Update state
    stateManager.setActivePersona(persona.name);

    // Return persona data with status bar info
    res.json({
      result: {
        success: true,
        persona: persona.name,
        title: persona.title,
        description: persona.description,
        content: content,
        availableTasks: persona.tasks || []
      },
      statusBar: {
        text: `Active Persona: ${persona.title}`,
        icon: '🧠'
      }
    });
  } catch (error) {
    console.error('Error in change-persona tool:', error);
    res.status(500).json({
      error: error.message,
      statusBar: {
        text: `Error: ${error.message}`,
        icon: '⚠️'
      }
    });
  }
});

// Fetch template tool
router.post('/fetch-template', async (req, res) => {
  try {
    console.log('Received fetch-template request:', req.body);
    const { templateName } = req.body.params || {};
    
    if (!templateName) {
      return res.status(400).json({
        error: 'Missing required parameter: templateName',
        statusBar: {
          text: `Error: Missing template name`,
          icon: '⚠️'
        }
      });
    }
    
    console.log(`Fetching template: ${templateName}`);
    
    // Fetch template from Firebase
    const content = await apiClient.getFile('template', `${templateName}.md`);
    
    // Return template data
    res.json({
      result: {
        success: true,
        template: templateName,
        content: content
      },
      statusBar: {
        text: `Template loaded: ${templateName}`,
        icon: '📝'
      }
    });
  } catch (error) {
    console.error('Error in fetch-template tool:', error);
    res.status(500).json({ 
      error: error.message,
      statusBar: {
        text: `Error: ${error.message}`,
        icon: '⚠️'
      }
    });
  }
});

// Run checklist tool
router.post('/run-checklist', async (req, res) => {
  try {
    console.log('Received run-checklist request:', req.body);
    const { checklistName, documentContent } = req.body.params || {};
    
    if (!checklistName) {
      return res.status(400).json({
        error: 'Missing required parameter: checklistName',
        statusBar: {
          text: `Error: Missing checklist name`,
          icon: '⚠️'
        }
      });
    }
    
    if (!documentContent) {
      return res.status(400).json({
        error: 'Missing required parameter: documentContent',
        statusBar: {
          text: `Error: Missing document content`,
          icon: '⚠️'
        }
      });
    }
    
    console.log(`Running checklist: ${checklistName}`);
    
    // Fetch checklist from Firebase
    const checklistContent = await apiClient.getFile('checklist', `${checklistName}.md`);
    
    // Here we would actually analyze the document against the checklist
    // This is a simplified example
    const results = {
      passed: true,
      items: [],
      recommendations: []
    };
    
    // Parse checklist items
    const checklistLines = checklistContent.split('\n');
    let currentSection = '';
    
    for (const line of checklistLines) {
      if (line.startsWith('##')) {
        currentSection = line.replace('##', '').trim();
      } else if (line.includes('- [ ]')) {
        const item = line.replace('- [ ]', '').trim();
        const passed = documentContent.toLowerCase().includes(item.toLowerCase());
        
        results.items.push({
          section: currentSection,
          item,
          passed
        });
        
        if (!passed) {
          results.passed = false;
          results.recommendations.push(`Consider addressing: ${item} in section ${currentSection}`);
        }
      }
    }
    
    // Return checklist results
    res.json({
      result: {
        success: true,
        checklist: checklistName,
        results
      },
      statusBar: {
        text: results.passed ? `✅ Checklist passed` : `⚠️ Checklist: ${results.recommendations.length} issues found`,
        icon: results.passed ? '✅' : '⚠️'
      }
    });
  } catch (error) {
    console.error('Error in run-checklist tool:', error);
    res.status(500).json({ 
      error: error.message,
      statusBar: {
        text: `Error: ${error.message}`,
        icon: '⚠️'
      }
    });
  }
});

// Load task tool
router.post('/load-task', async (req, res) => {
  try {
    console.log('Received load-task request:', req.body);
    const { taskName, personaName } = req.body.params || {};

    if (!taskName) {
      return res.status(400).json({
        error: 'Missing required parameter: taskName',
        statusBar: {
          text: `Error: Missing task name`,
          icon: '⚠️'
        }
      });
    }

    console.log(`Loading task: ${taskName}`);

    // Load config if not already loaded
    let config = configService.getConfig();
    if (!config) {
      config = await configService.loadConfig();
    }

    // Get current persona or use provided one
    const currentPersona = personaName || stateManager.getActivePersona();
    let taskInfo = null;
    let taskFile = `${taskName}.md`;

    // If we have a persona, try to find the task in that persona's tasks
    if (currentPersona) {
      const persona = configService.findPersona(currentPersona);
      if (persona && persona.tasks) {
        const task = persona.tasks.find(t =>
          t.name === taskName ||
          t.name.toLowerCase() === taskName.toLowerCase()
        );
        if (task) {
          taskInfo = task;
          taskFile = task.taskFile;
        }
      }
    }

    // Fetch task from Firebase using config-resolved path
    const dataResolution = configService.getDataResolution();
    const content = await apiClient.getFile('task', taskFile);

    // Update state
    stateManager.setSessionContext('currentTask', taskName);
    if (taskInfo) {
      stateManager.setSessionContext('currentTaskInfo', taskInfo);
    }

    // Return task data
    res.json({
      result: {
        success: true,
        task: taskName,
        taskInfo: taskInfo,
        content: content,
        persona: currentPersona
      },
      statusBar: {
        text: `Active Task: ${taskInfo ? taskInfo.description : taskName}`,
        icon: '📋'
      }
    });
  } catch (error) {
    console.error('Error in load-task tool:', error);
    res.status(500).json({
      error: error.message,
      statusBar: {
        text: `Error: ${error.message}`,
        icon: '⚠️'
      }
    });
  }
});

// Save context tool
router.post('/save-context', async (req, res) => {
  try {
    console.log('Received save-context request:', req.body);
    const { contextName, contextData, useFirebase } = req.body.params || {};

    if (!contextName) {
      return res.status(400).json({
        error: 'Missing required parameter: contextName',
        statusBar: {
          text: `Error: Missing context name`,
          icon: '⚠️'
        }
      });
    }

    if (!contextData) {
      return res.status(400).json({
        error: 'Missing required parameter: contextData',
        statusBar: {
          text: `Error: Missing context data`,
          icon: '⚠️'
        }
      });
    }

    console.log(`Saving context: ${contextName}`);

    // Use context service for enhanced context management
    const result = await contextService.saveContext(contextName, contextData, {
      useFirebase: useFirebase || false,
      creator: 'mcp-server'
    });

    // Return success
    res.json({
      result: {
        success: result.success,
        contextName: result.contextName,
        storedIn: result.storedIn
      },
      statusBar: {
        text: `Context saved: ${contextName}`,
        icon: '💾'
      }
    });
  } catch (error) {
    console.error('Error in save-context tool:', error);
    res.status(500).json({
      error: error.message,
      statusBar: {
        text: `Error: ${error.message}`,
        icon: '⚠️'
      }
    });
  }
});

// Load context tool
router.post('/load-context', async (req, res) => {
  try {
    console.log('Received load-context request:', req.body);
    const { contextName, useFirebase } = req.body.params || {};

    if (!contextName) {
      return res.status(400).json({
        error: 'Missing required parameter: contextName',
        statusBar: {
          text: `Error: Missing context name`,
          icon: '⚠️'
        }
      });
    }

    console.log(`Loading context: ${contextName}`);

    // Use context service to load context
    const result = await contextService.loadContext(contextName, {
      useFirebase: useFirebase || false
    });

    // Return loaded context
    res.json({
      result: {
        success: result.success,
        contextName: result.contextName,
        contextData: result.contextData,
        source: result.source
      },
      statusBar: {
        text: `Context loaded: ${contextName}`,
        icon: '📂'
      }
    });
  } catch (error) {
    console.error('Error in load-context tool:', error);
    res.status(500).json({
      error: error.message,
      statusBar: {
        text: `Error: ${error.message}`,
        icon: '⚠️'
      }
    });
  }
});

// Get config tool
router.get('/get-config', async (req, res) => {
  try {
    console.log('Received get-config request');

    // Try to get config from cache first
    let config = configService.getConfig();

    // If not in cache, load from Firebase
    if (!config) {
      console.log('Config not in cache, loading from Firebase');
      config = await configService.loadConfig();
    }

    // Get config metadata
    const metadata = configService.getConfigMetadata();

    res.json({
      result: {
        success: true,
        config: config,
        metadata: metadata
      },
      statusBar: {
        text: `Config loaded: ${config.title} v${config.version}`,
        icon: '⚙️'
      }
    });
  } catch (error) {
    console.error('Error in get-config tool:', error);
    res.status(500).json({
      error: error.message,
      statusBar: {
        text: `Error loading config: ${error.message}`,
        icon: '⚠️'
      }
    });
  }
});

// Get state tool (for debugging)
router.get('/state', (req, res) => {
  try {
    const state = stateManager.getState();
    res.json({
      result: state,
      statusBar: {
        text: `State retrieved`,
        icon: '🔍'
      }
    });
  } catch (error) {
    console.error('Error in get-state tool:', error);
    res.status(500).json({
      error: error.message
    });
  }
});

// Clear state tool (for debugging)
router.post('/clear-state', (req, res) => {
  try {
    const state = stateManager.clearState();
    res.json({
      result: {
        success: true,
        state
      },
      statusBar: {
        text: `State cleared`,
        icon: '🧹'
      }
    });
  } catch (error) {
    console.error('Error in clear-state tool:', error);
    res.status(500).json({ 
      error: error.message 
    });
  }
});

module.exports = router;