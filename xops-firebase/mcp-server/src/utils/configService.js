/**
 * Config Service - Manage configuration loading and parsing
 * 
 * This service provides functionality to load, parse, and validate
 * the XOps configuration file from Firebase Storage.
 */

const ApiClient = require('./apiClient');
const stateManager = require('./stateManager');

class ConfigService {
  constructor(firebaseEndpoint, apiKey) {
    this.apiClient = new ApiClient(firebaseEndpoint, apiKey);
    this.configCache = null;
    this.lastLoadTime = null;
    this.cacheTimeout = 5 * 60 * 1000; // 5 minutes
    
    console.log('Config Service initialized');
  }
  
  /**
   * Load and parse the configuration file
   * @param {object} options - Optional parameters
   * @returns {Promise<object>} Parsed configuration object
   */
  async loadConfig(options = {}) {
    try {
      console.log('Loading configuration from Firebase');
      
      // Check cache if not forcing reload
      if (!options.forceReload && this.isCacheValid()) {
        console.log('Using cached configuration');
        return this.configCache;
      }
      
      // Fetch config file from Firebase
      const configContent = await this.apiClient.getFile('config', 'config.json');
      
      // Parse JSON content
      let config;
      try {
        config = JSON.parse(configContent);
      } catch (parseError) {
        throw new Error(`Invalid JSON in config file: ${parseError.message}`);
      }
      
      // Validate configuration structure
      this.validateConfig(config);
      
      // Cache the config
      this.configCache = config;
      this.lastLoadTime = new Date();
      
      // Store in state manager
      stateManager.setConfig(config);
      
      console.log(`Configuration loaded successfully: ${config.title} v${config.version}`);
      return config;
    } catch (error) {
      console.error('Error loading configuration:', error);
      throw new Error(`Failed to load configuration: ${error.message}`);
    }
  }
  
  /**
   * Validate configuration structure
   * @param {object} config - Configuration object to validate
   * @throws {Error} If configuration is invalid
   */
  validateConfig(config) {
    const requiredFields = ['title', 'version', 'dataResolution', 'personas'];
    
    for (const field of requiredFields) {
      if (!config[field]) {
        throw new Error(`Missing required configuration field: ${field}`);
      }
    }
    
    // Validate dataResolution structure
    const requiredDataTypes = ['personas', 'tasks', 'templates', 'checklists'];
    for (const dataType of requiredDataTypes) {
      if (!config.dataResolution[dataType]) {
        throw new Error(`Missing data resolution for: ${dataType}`);
      }
    }
    
    // Validate personas array
    if (!Array.isArray(config.personas) || config.personas.length === 0) {
      throw new Error('Configuration must contain at least one persona');
    }
    
    // Validate each persona
    config.personas.forEach((persona, index) => {
      if (!persona.name || !persona.title || !persona.persona) {
        throw new Error(`Invalid persona at index ${index}: missing required fields`);
      }
    });
    
    console.log('Configuration validation passed');
  }
  
  /**
   * Get configuration (from cache or state manager)
   * @returns {object|null} Configuration object or null if not loaded
   */
  getConfig() {
    // Try cache first
    if (this.isCacheValid()) {
      return this.configCache;
    }
    
    // Try state manager
    const stateConfig = stateManager.getConfig();
    if (stateConfig) {
      this.configCache = stateConfig;
      this.lastLoadTime = new Date();
      return stateConfig;
    }
    
    return null;
  }
  
  /**
   * Get available personas from configuration
   * @returns {Array} Array of persona objects
   */
  getPersonas() {
    const config = this.getConfig();
    return config ? config.personas : [];
  }
  
  /**
   * Find a persona by name or title
   * @param {string} identifier - Persona name or title
   * @returns {object|null} Persona object or null if not found
   */
  findPersona(identifier) {
    const personas = this.getPersonas();
    return personas.find(p => 
      p.name === identifier || 
      p.title === identifier ||
      p.name.toLowerCase() === identifier.toLowerCase() ||
      p.title.toLowerCase() === identifier.toLowerCase()
    ) || null;
  }
  
  /**
   * Get tasks for a specific persona
   * @param {string} personaIdentifier - Persona name or title
   * @returns {Array} Array of task objects
   */
  getPersonaTasks(personaIdentifier) {
    const persona = this.findPersona(personaIdentifier);
    return persona && persona.tasks ? persona.tasks : [];
  }
  
  /**
   * Get data resolution paths
   * @returns {object} Data resolution configuration
   */
  getDataResolution() {
    const config = this.getConfig();
    return config ? config.dataResolution : {};
  }
  
  /**
   * Resolve file path based on configuration
   * @param {string} fileType - Type of file (persona, task, template, checklist, data)
   * @param {string} fileName - Name of the file
   * @returns {string} Resolved file path
   */
  resolveFilePath(fileType, fileName) {
    const dataResolution = this.getDataResolution();
    const basePath = dataResolution[fileType];
    
    if (!basePath) {
      throw new Error(`Unknown file type: ${fileType}`);
    }
    
    // Ensure fileName has proper extension if not provided
    if (!fileName.includes('.')) {
      const extension = fileType === 'config' ? '.json' : '.md';
      fileName += extension;
    }
    
    return `${basePath}/${fileName}`;
  }
  
  /**
   * Check if cached configuration is still valid
   * @returns {boolean} True if cache is valid
   */
  isCacheValid() {
    if (!this.configCache || !this.lastLoadTime) {
      return false;
    }
    
    const now = new Date();
    const timeDiff = now.getTime() - this.lastLoadTime.getTime();
    return timeDiff < this.cacheTimeout;
  }
  
  /**
   * Clear configuration cache
   */
  clearCache() {
    console.log('Clearing configuration cache');
    this.configCache = null;
    this.lastLoadTime = null;
  }
  
  /**
   * Get configuration metadata
   * @returns {object} Configuration metadata
   */
  getConfigMetadata() {
    const config = this.getConfig();
    if (!config) {
      return { loaded: false };
    }
    
    return {
      loaded: true,
      title: config.title,
      version: config.version,
      personaCount: config.personas.length,
      lastLoadTime: this.lastLoadTime,
      cacheValid: this.isCacheValid()
    };
  }
}

// Create a singleton instance
const configService = new ConfigService(
  process.env.FIREBASE_ENDPOINT,
  process.env.API_KEY
);

module.exports = configService;
