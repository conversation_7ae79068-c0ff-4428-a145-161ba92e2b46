class StateManager {
  constructor() {
    this.state = {
      activePersona: null,
      sessionContext: {},
      preferences: {},
      config: null,
      lastConfigLoad: null
    };

    console.log('State Manager initialized');
  }

  setActivePersona(persona) {
    console.log(`Setting active persona to: ${persona}`);
    this.state.activePersona = persona;
    this.state.sessionContext.lastPersonaChange = new Date().toISOString();
    return this.state.activePersona;
  }

  getActivePersona() {
    return this.state.activePersona;
  }

  setSessionContext(key, value) {
    console.log(`Setting session context ${key} to:`, value);
    this.state.sessionContext[key] = value;
    this.state.sessionContext.lastUpdate = new Date().toISOString();
    return this.state.sessionContext[key];
  }

  getSessionContext(key) {
    if (key) {
      return this.state.sessionContext[key];
    }
    return this.state.sessionContext;
  }

  setConfig(config) {
    console.log('Setting configuration in state manager');
    this.state.config = config;
    this.state.lastConfigLoad = new Date().toISOString();
    return this.state.config;
  }

  getConfig() {
    return this.state.config;
  }

  isConfigLoaded() {
    return this.state.config !== null;
  }

  setPreference(key, value) {
    console.log(`Setting preference ${key} to:`, value);
    this.state.preferences[key] = value;
    return this.state.preferences[key];
  }

  getPreference(key) {
    if (key) {
      return this.state.preferences[key];
    }
    return this.state.preferences;
  }

  getState() {
    return {
      ...this.state,
      metadata: {
        stateVersion: '1.0.0',
        lastUpdate: new Date().toISOString(),
        configLoaded: this.isConfigLoaded()
      }
    };
  }

  clearState() {
    const oldState = { ...this.state };
    this.state = {
      activePersona: null,
      sessionContext: {},
      preferences: {},
      config: null,
      lastConfigLoad: null
    };
    console.log('State cleared');
    return {
      success: true,
      previousState: oldState,
      newState: this.state
    };
  }

  // Persistence methods (for future implementation)
  async saveStateToPersistence() {
    // TODO: Implement state persistence to Firebase or local storage
    console.log('State persistence not yet implemented');
    return { success: false, message: 'Persistence not implemented' };
  }

  async loadStateFromPersistence() {
    // TODO: Implement state loading from Firebase or local storage
    console.log('State loading from persistence not yet implemented');
    return { success: false, message: 'Persistence loading not implemented' };
  }
}

// Create a singleton instance
const stateManager = new StateManager();
module.exports = stateManager;